import arc from '@architect/functions';
import type { APIGatewayEvent, APIGatewayProxyResult } from 'aws-lambda';
import { Batch } from 'aws-sdk';
import Sftp from 'ssh2-sftp-client';


export const handler = async (event: APIGatewayEvent): Promise<APIGatewayProxyResult> => {
  console.log('IEX batch function executed at:', new Date().toISOString());


console.log('#5', 'deleted');

  return {
    statusCode: 200,
    body: JSON.stringify({ }),
  };
};
