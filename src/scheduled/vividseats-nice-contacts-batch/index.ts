import type { APIGatewayProxyResult } from 'aws-lambda';

interface NiceContact {
  abandoned: boolean;
  stateId: number;
  contactStartDate: string;
  preQueueSeconds: number;
  lastUpdateTime: string;
  mediaTypeId: number;
  agentId: number | null;
  contactId: string;
  isActive: boolean;
  agentStartDate: string | null;
  skillId: number | null;
  skillName: string | null;
  campaignId: number | null;
}

interface NiceContactsResponse {
  contacts: NiceContact[];
  _links?: {
    next?: string;
  };
}

interface NiceAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

/**
 * Get authentication token from NICE API
 */
async function getNiceAuthToken(): Promise<string> {
  const authUrl = process.env.NICE_AUTH_URL || 'https://cxone.niceincontact.com/auth/token';
  const clientId = process.env.NICE_CLIENT_ID;
  const clientSecret = process.env.NICE_CLIENT_SECRET;
  const accessKeyId = process.env.NICE_ACCESS_KEY_ID;
  const secretKey = process.env.NICE_SECRET_KEY;

  if (!clientId || !clientSecret || !accessKeyId || !secretKey) {
    throw new Error('Missing required NICE API credentials in environment variables');
  }

  const response = await fetch(authUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      grant_type: 'password',
      client_id: clientId,
      client_secret: clientSecret,
      username: accessKeyId,
      password: secretKey,
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to get NICE auth token: ${response.status} - ${errorText}`);
  }

  const authData: NiceAuthResponse = await response.json();
  return authData.access_token;
}

/**
 * Fetch contacts from NICE API with pagination support
 */
async function fetchNiceContacts(startDate: Date, endDate: Date, accessToken: string): Promise<NiceContact[]> {
  const baseUrl = process.env.NICE_API_BASE_URL || 'https://api-c8.incontact.com/inContactAPI/services/v31.0';
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  const allContacts: NiceContact[] = [];

  async function fetchWithRetry(url: string, retries = 3): Promise<NiceContactsResponse> {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(url, { headers });
        if (!response.ok) {
          throw new Error(`HTTP ${response.status} - ${response.statusText}`);
        }
        return await response.json();
      } catch (error) {
        console.warn(`Retry ${attempt}/${retries} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        if (attempt === retries) throw error;
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before retry
      }
    }
    throw new Error('All retry attempts failed');
  }

  async function fetchPage(url: string): Promise<void> {
    const data = await fetchWithRetry(url);
    allContacts.push(...(data.contacts || []));

    // Follow pagination if there's a next link
    if (data._links?.next) {
      await fetchPage(data._links.next);
    }
  }

  const startDateStr = startDate.toISOString();
  const endDateStr = endDate.toISOString();
  const initialUrl = `${baseUrl}/contacts?startDate=${startDateStr}&endDate=${endDateStr}&top=10000`;

  await fetchPage(initialUrl);
  return allContacts;
}

export const handler = async (): Promise<APIGatewayProxyResult> => {
  console.log('NICE contacts batch function executed at:', new Date().toISOString());

  try {
    // Calculate time range for last 360 minutes
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - (360 * 60 * 1000)); // 360 minutes ago

    console.log(`Fetching contacts from ${startTime.toISOString()} to ${endTime.toISOString()}`);

    // Get NICE authentication token
    const accessToken = await getNiceAuthToken();

    // Fetch contacts from NICE
    const contacts = await fetchNiceContacts(startTime, endTime, accessToken);

    console.log(`Successfully retrieved ${contacts.length} contacts`);

    // TODO: Process and store contacts (implement based on your requirements)
    // This could involve:
    // - Storing in DynamoDB
    // - Sending to SQS queue for further processing
    // - Transforming data format

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Successfully processed NICE contacts',
        contactsCount: contacts.length,
        timeRange: {
          start: startTime.toISOString(),
          end: endTime.toISOString()
        }
      }),
    };
  } catch (error) {
    console.error('Error processing NICE contacts:', error);

    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Failed to process NICE contacts',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
    };
  }
};
