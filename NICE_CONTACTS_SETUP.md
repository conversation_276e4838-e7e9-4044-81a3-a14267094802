# NICE Contacts <PERSON><PERSON> Handler Setup

This document explains how to configure the NICE contacts batch handler that retrieves the last 360 minutes of contact information from the NICE /conversations API.

## Overview

The scheduled batch handler (`src/scheduled/vividseats-nice-contacts-batch/index.ts`) runs every 15 minutes (as configured in `app.arc`) and fetches contact data from the NICE API for the previous 360 minutes (6 hours).

## Required Environment Variables

The following environment variables must be set for the handler to work:

### NICE API Credentials
- `NICE_CLIENT_ID` - Your NICE API client ID
- `NICE_CLIENT_SECRET` - Your NICE API client secret  
- `NICE_ACCESS_KEY_ID` - Your NICE API access key ID
- `NICE_SECRET_KEY` - Your NICE API secret key

### Optional Configuration
- `NICE_AUTH_URL` - NICE authentication endpoint (defaults to `https://cxone.niceincontact.com/auth/token`)
- `NICE_API_BASE_URL` - NICE API base URL (defaults to `https://api-c8.incontact.com/inContactAPI/services/v31.0`)

## Setting Environment Variables

### For Local Development
Create a `.env` file in the project root:

```bash
NICE_CLIENT_ID=your_client_id_here
NICE_CLIENT_SECRET=your_client_secret_here
NICE_ACCESS_KEY_ID=your_access_key_id_here
NICE_SECRET_KEY=your_secret_key_here
```

### For AWS Deployment
Use AWS Systems Manager Parameter Store or AWS Secrets Manager to store these credentials securely. The Architect framework supports automatic environment variable injection from SSM parameters.

## Features

- **Automatic Authentication**: Handles NICE API token acquisition
- **Pagination Support**: Automatically follows pagination links to retrieve all contacts
- **Retry Logic**: Implements retry mechanism with exponential backoff for failed requests
- **Error Handling**: Comprehensive error handling with detailed logging
- **Time Range**: Automatically calculates the last 360 minutes from execution time

## Data Retrieved

The handler fetches the following contact information:
- Contact ID and basic metadata
- Agent information (ID, start date)
- Contact timing (start date, queue time, last update)
- Contact state and status
- Media type and skill information
- Campaign details

## Next Steps

The current implementation retrieves and logs the contact data. You'll need to implement the data processing logic based on your requirements:

1. **Storage**: Store contacts in DynamoDB table
2. **Processing**: Send to SQS queue for further processing
3. **Transformation**: Convert to required format for downstream systems
4. **Deduplication**: Handle duplicate contacts across batch runs

## Monitoring

The handler logs:
- Execution timestamps
- Number of contacts retrieved
- Time range processed
- Any errors encountered

Monitor CloudWatch logs for the scheduled function to track performance and troubleshoot issues.
