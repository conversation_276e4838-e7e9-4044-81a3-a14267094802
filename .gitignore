# Node modules (top-level and per-lambda)
node_modules/
**/node_modules/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log

# Architect artifacts
.arc
.arc-config
.arc-params
public/_static/
__ARC__
.arcignore
.arc-output
.arc-env

# Local sandbox files and temporary databases
local-env.json
db.arc
tmp/
tmp.db
sandbox.db

# Test output
coverage/
*.lcov

# Build output
dist/
vendor/
.build/


# Environment
.env
.env.*

# OS-specific
.DS_Store
Thumbs.db

# IDE-specific
.vscode/
.idea/
*.sublime-workspace
*.sublime-project
