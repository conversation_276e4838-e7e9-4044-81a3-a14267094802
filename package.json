{"name": "service-base", "version": "0.0.0", "description": "A fresh new Architect project!", "scripts": {"start": "npx sandbox"}, "devDependencies": {"@architect/architect": "^11.2.2", "@architect/plugin-lambda-invoker": "^2.0.2", "@architect/plugin-typescript": "^2.1.0", "@types/node": "^22.15.30", "esbuild-node-externals": "^1.18.0"}, "dependencies": {"@architect/functions": "^8.1.9", "@aws-sdk/client-dynamodb": "^3.826.0", "@aws-sdk/lib-dynamodb": "^3.826.0", "aws-sdk": "^2.1692.0", "jsonata": "^2.0.6", "ssh2-sftp-client": "^12.0.0", "uuid": "^11.1.0"}}