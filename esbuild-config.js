const { nodeExternalsPlugin } = require('esbuild-node-externals');

module.exports = {
  plugins: [
    // This plugin excludes node_modules from bundling
    nodeExternalsPlugin({
      // Don't exclude these packages - they'll be bundled
      allowList: [
        '@architect/functions',
        '@architect/functions',
        '@aws-sdk/client-dynamodb',
        '@aws-sdk/lib-dynamodb',
        'aws-sdk',
        'ssh2-sftp-client',
        'uuid'
      ],
    }),
  ],
  // Tell esbuild to ignore .node files
  loader: {
    '.node': 'file'
  },
}
